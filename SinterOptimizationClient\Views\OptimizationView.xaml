<UserControl x:Class="SinterOptimizationClient.Views.OptimizationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:views="clr-namespace:SinterOptimizationClient.Views"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="1600">

    <Grid Background="{StaticResource LightGrayBrush}">
        <Grid.RowDefinitions>
            <!-- 物料信息表 - 占50%高度 -->
            <RowDefinition Height="0.5*"/>
            <!-- 优化计算区 - 占50%高度 -->
            <RowDefinition Height="0.5*"/>
        </Grid.RowDefinitions>

        <!-- 物料信息表区域 -->
        <Border Grid.Row="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,1">
            <views:MaterialDataView DataContext="{Binding MaterialDataViewModel}"/>
        </Border>

        <!-- 优化计算区 - 横向两个区域 -->
        <Grid Grid.Row="1" Background="{StaticResource LightGrayBrush}">
            <Grid.ColumnDefinitions>
                <!-- 区域1：配料优化目标 - 占25% -->
                <ColumnDefinition Width="0.25*"/>
                <!-- 区域2：约束条件 - 占75% -->
                <ColumnDefinition Width="0.75*"/>
            </Grid.ColumnDefinitions>

            <!-- 区域1：配料优化目标 -->
            <Border Grid.Column="0"
                    Background="#F9FAFB"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="0,0,1,0"
                    Padding="12">
                <StackPanel>
                    <!-- 标题 -->
                    <TextBlock Text="配料优化目标"
                               FontSize="14"
                               FontWeight="Bold"
                               Foreground="{StaticResource PrimaryBrush}"
                               Margin="0,0,0,8">
                        <TextBlock.TextDecorations>
                            <TextDecorationCollection>
                                <TextDecoration Location="Underline">
                                    <TextDecoration.Pen>
                                        <Pen Brush="{StaticResource OrangeBrush}" Thickness="1"/>
                                    </TextDecoration.Pen>
                                </TextDecoration>
                            </TextDecorationCollection>
                        </TextBlock.TextDecorations>
                    </TextBlock>

                    <!-- 优化目标选择 -->
                    <StackPanel Margin="0,0,0,12">
                        <RadioButton Content="成本最优" FontSize="12"
                                     IsChecked="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CostOptimal}"
                                     Margin="0,0,0,6">
                            <RadioButton.ToolTip>
                                <ToolTip Content="优先降低吨矿成本，满足约束条件"/>
                            </RadioButton.ToolTip>
                        </RadioButton>
                        <TextBlock Text="* 优先降低吨矿成本"
                                   FontSize="10"
                                   Foreground="#6B7280"
                                   Margin="18,0,0,6"/>

                        <RadioButton Content="质量最优" FontSize="12"
                                     IsChecked="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}"
                                     Margin="0,0,0,6">
                            <RadioButton.ToolTip>
                                <ToolTip Content="优先缩小成分偏差，满足约束条件"/>
                            </RadioButton.ToolTip>
                        </RadioButton>
                        <TextBlock Text="* 优先缩小成分偏差"
                                   FontSize="10"
                                   Foreground="#6B7280"
                                   Margin="18,0,0,12"/>
                    </StackPanel>

                    <!-- 算法选择 -->
                    <StackPanel Margin="0,0,0,12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="优化算法:" VerticalAlignment="Center" FontSize="12" Margin="0,0,8,0"/>
                            <ComboBox Grid.Column="1"
                                      SelectedItem="{Binding Parameters.AlgorithmType}"
                                      FontSize="11"
                                      Height="24">
                                <ComboBoxItem Content="SQP二次序列算法"/>
                                <ComboBoxItem Content="其他算法（预留）" IsEnabled="False"/>
                            </ComboBox>
                        </Grid>
                        <TextBlock Text="* SQP算法，精度±0.01"
                                   FontSize="10"
                                   Foreground="#6B7280"
                                   Margin="0,3,0,0"/>
                    </StackPanel>

                    <!-- 计划日产设置 -->
                    <StackPanel>
                        <TextBlock Text="生产参数设置" FontSize="12" FontWeight="Bold" Margin="0,0,0,6"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="计划日产:" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="0" Grid.Column="1"
                                     Text="{Binding Parameters.PlannedDailyOutput, StringFormat=F0}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="3,1"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="吨" VerticalAlignment="Center" FontSize="11" Margin="3,0,0,0"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="金属收得率:" VerticalAlignment="Center"/>
                            <TextBox Grid.Row="1" Grid.Column="1"
                                     Text="{Binding Parameters.MetalRecoveryRate, StringFormat=F0}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     Margin="5,2"/>
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="出矿率:" VerticalAlignment="Center"/>
                            <TextBox Grid.Row="2" Grid.Column="1"
                                     Text="{Binding Parameters.SinterYield, StringFormat=F0}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     Margin="5,2"/>
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="作业率:" VerticalAlignment="Center"/>
                            <TextBox Grid.Row="3" Grid.Column="1"
                                     Text="{Binding Parameters.OperationRate, StringFormat=F0}"
                                     Style="{StaticResource InputTextBoxStyle}"
                                     Margin="5,2"/>
                            <TextBlock Grid.Row="3" Grid.Column="2" Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>
                        </Grid>
                    </StackPanel>

                    <!-- 配料优化目标确认按钮 -->
                    <StackPanel Margin="0,15,0,0">
                        <Button Content="确定优化目标"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Command="{Binding ConfirmOptimizationTargetCommand}"
                                Width="150"
                                Height="35"
                                HorizontalAlignment="Center"
                                Background="#F97316"
                                Margin="0,0,0,10"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 区域2：约束条件设置 -->
            <Border Grid.Column="1"
                    Background="#F9FAFB"
                    Padding="12">
                <StackPanel>
                    <!-- 标题 -->
                    <TextBlock Text="约束条件设置"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="{StaticResource PrimaryBrush}"
                               Margin="0,0,0,12">
                        <TextBlock.TextDecorations>
                            <TextDecorationCollection>
                                <TextDecoration Location="Underline">
                                    <TextDecoration.Pen>
                                        <Pen Brush="{StaticResource OrangeBrush}" Thickness="1"/>
                                    </TextDecoration.Pen>
                                </TextDecoration>
                            </TextDecorationCollection>
                        </TextBlock.TextDecorations>
                    </TextBlock>

                    <!-- 模块1：成分范围约束和目标设定 -->
                    <StackPanel Margin="0,0,0,12">
                        <TextBlock Text="成分范围约束和目标设定"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Foreground="{StaticResource PrimaryBrush}"
                                   Margin="0,0,0,8"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="65"/>
                                <ColumnDefinition Width="65"/>
                                <ColumnDefinition Width="15"/>
                                <ColumnDefinition Width="65"/>
                                <ColumnDefinition Width="35"/>
                                <ColumnDefinition Width="65"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="22"/>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                                <RowDefinition Height="26"/>
                            </Grid.RowDefinitions>

                            <!-- 表头 -->
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="最小值" HorizontalAlignment="Center" FontSize="11" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="最大值" HorizontalAlignment="Center" FontSize="11" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="5" Text="目标值" HorizontalAlignment="Center" FontSize="11" FontWeight="Bold"/>

                            <!-- TFe -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="TFe(%):" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Parameters.TfeMin, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding Parameters.TfeMax, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="1" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="1" Grid.Column="5" Text="{Binding Parameters.TfeTarget, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>

                            <!-- SiO2 -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="SiO₂(%):" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Parameters.Sio2Min, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding Parameters.Sio2Max, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="2" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="2" Grid.Column="5" Text="{Binding Parameters.Sio2Target, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>

                            <!-- CaO -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="CaO(%):" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Parameters.CaoMin, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="3" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="3" Grid.Column="3" Text="{Binding Parameters.CaoMax, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="3" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="3" Grid.Column="5" Text="{Binding Parameters.CaoTarget, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>

                            <!-- MgO -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="MgO(%):" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Parameters.MgoMin, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="4" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="4" Grid.Column="3" Text="{Binding Parameters.MgoMax, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="4" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="4" Grid.Column="5" Text="{Binding Parameters.MgoTarget, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>

                            <!-- Al2O3 -->
                            <TextBlock Grid.Row="5" Grid.Column="0" Text="Al₂O₃(%):" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding Parameters.Al2o3Min, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="5" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="5" Grid.Column="3" Text="{Binding Parameters.Al2o3Max, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                            <TextBlock Grid.Row="5" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="5" Grid.Column="5" Text="{Binding Parameters.Al2o3Target, StringFormat=F1}" Height="20" FontSize="11" Padding="2" Margin="1"/>

                            <!-- 成本约束 -->
                            <TextBlock Grid.Row="6" Grid.Column="0" Text="成本上限(元/吨):" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Row="6" Grid.Column="3" Text="{Binding Parameters.CostTargetMax, StringFormat=F0}" Height="20" FontSize="11" Padding="2" Margin="1"/>
                        </Grid>
                    </StackPanel>

                                <!-- 表头 -->
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="最小值" HorizontalAlignment="Center" FontSize="11" FontWeight="Bold" Margin="0,0,0,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="3" Text="最大值" HorizontalAlignment="Center" FontSize="11" FontWeight="Bold" Margin="0,0,0,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="5" Text="目标值" HorizontalAlignment="Center" FontSize="11" FontWeight="Bold" Margin="0,0,0,2"/>

                                <!-- TFe -->
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="TFe(%):" VerticalAlignment="Center" FontSize="11"/>
                                <TextBox Grid.Row="1" Grid.Column="1"
                                         Text="{Binding Parameters.TfeMin, StringFormat=F1}"
                                         Height="22" FontSize="11"
                                         Padding="3,2" Margin="2,1"/>
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBox Grid.Row="1" Grid.Column="3"
                                         Text="{Binding Parameters.TfeMax, StringFormat=F1}"
                                         Height="22" FontSize="11"
                                         Padding="3,2" Margin="2,1"/>
                                <TextBlock Grid.Row="1" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                                <TextBox Grid.Row="1" Grid.Column="5"
                                         Text="{Binding Parameters.TfeTarget, StringFormat=F1}"
                                         Height="22" FontSize="11"
                                         Padding="3,2" Margin="2,1"/>

                            <!-- SiO2 -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="SiO₂(%):" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="2" Grid.Column="1"
                                     Text="{Binding Parameters.Sio2Min, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="2" Grid.Column="3"
                                     Text="{Binding Parameters.Sio2Max, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="2" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="2" Grid.Column="5"
                                     Text="{Binding Parameters.Sio2Target, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>

                            <!-- CaO -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="CaO(%):" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="3" Grid.Column="1"
                                     Text="{Binding Parameters.CaoMin, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="3" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="3" Grid.Column="3"
                                     Text="{Binding Parameters.CaoMax, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="3" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="3" Grid.Column="5"
                                     Text="{Binding Parameters.CaoTarget, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>

                            <!-- MgO -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="MgO(%):" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="4" Grid.Column="1"
                                     Text="{Binding Parameters.MgoMin, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="4" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="4" Grid.Column="3"
                                     Text="{Binding Parameters.MgoMax, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="4" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="4" Grid.Column="5"
                                     Text="{Binding Parameters.MgoTarget, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>

                            <!-- Al2O3 -->
                            <TextBlock Grid.Row="5" Grid.Column="0" Text="Al₂O₃(%):" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="5" Grid.Column="1"
                                     Text="{Binding Parameters.Al2o3Min, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="5" Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="5" Grid.Column="3"
                                     Text="{Binding Parameters.Al2o3Max, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Row="5" Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Row="5" Grid.Column="5"
                                     Text="{Binding Parameters.Al2o3Target, StringFormat=F1}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>

                            <!-- 成本约束 -->
                            <TextBlock Grid.Row="6" Grid.Column="0" Text="成本上限(元/吨):" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Row="6" Grid.Column="3"
                                     Text="{Binding Parameters.CostTargetMax, StringFormat=F0}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                        </Grid>
                    </StackPanel>

                    <!-- 右列：湿配比比例约束 -->
                    <StackPanel Grid.Column="1" Margin="8,0,0,0">
                        <TextBlock Text="湿配比比例约束" FontSize="12" FontWeight="Bold" Margin="0,0,0,6"/>

                        <Button Content="统一设置湿配比范围"
                                Background="{StaticResource PrimaryBrush}"
                                Foreground="White"
                                FontSize="11"
                                Height="26"
                                Command="{Binding SetUniformRatioRangeCommand}"
                                HorizontalAlignment="Left"
                                Margin="0,0,0,6"/>

                        <ScrollViewer MaxHeight="180" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding Materials}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="0,1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="50"/>
                                                <ColumnDefinition Width="15"/>
                                                <ColumnDefinition Width="50"/>
                                                <ColumnDefinition Width="15"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="{Binding Name}" VerticalAlignment="Center" FontSize="10"/>
                                            <TextBox Grid.Column="1"
                                                     Text="{Binding MinRatio, StringFormat=F1}"
                                                     Height="22" FontSize="11"
                                                     Padding="3,2" Margin="1"/>
                                            <TextBlock Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                                            <TextBox Grid.Column="3"
                                                     Text="{Binding MaxRatio, StringFormat=F1}"
                                                     Height="22" FontSize="11"
                                                     Padding="3,2" Margin="1"/>
                                            <TextBlock Grid.Column="4" Text="%" VerticalAlignment="Center" FontSize="10"/>
                                        </Grid>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </Grid>

                    <!-- 组2：碱度约束 -->
                    <StackPanel Margin="0,0,0,8">
                        <TextBlock Text="碱度（Ro）约束和目标设定" FontSize="12" FontWeight="Bold" Margin="0,0,0,6"/>

                        <StackPanel Margin="0,0,0,6">
                            <RadioButton Content="Ro=CaO/SiO₂" FontSize="11"
                                         IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoSio2}"
                                         Margin="0,0,0,3"/>
                            <RadioButton Content="Ro=(CaO+MgO)/(SiO₂+Al₂O₃)" FontSize="11"
                                         IsChecked="{Binding Parameters.RoCalculationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CaoMgoSio2Al2o3}"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="60"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="70"/>
                                <ColumnDefinition Width="40"/>
                                <ColumnDefinition Width="70"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="Ro范围:" VerticalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding Parameters.RoMin, StringFormat=F2}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Column="2" Text="~" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBox Grid.Column="3"
                                     Text="{Binding Parameters.RoMax, StringFormat=F2}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                            <TextBlock Grid.Column="4" Text="目标:" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="10"/>
                            <TextBox Grid.Column="5"
                                     Text="{Binding Parameters.RoTarget, StringFormat=F2}"
                                     Height="22" FontSize="11"
                                     Padding="3,2" Margin="2,1"/>
                        </Grid>
                    </StackPanel>

                    <!-- 组3：人工初选功能 -->
                    <StackPanel Margin="0,0,0,8">
                        <TextBlock Text="物料初选" FontSize="12" FontWeight="Bold" Margin="0,0,0,6"/>

                        <Button Content="物料选择"
                                Background="{StaticResource OrangeBrush}"
                                Foreground="White"
                                FontSize="11"
                                Height="28"
                                Command="{Binding ShowMaterialSelectionCommand}"
                                HorizontalAlignment="Left"
                                Margin="0,0,0,6"/>
                    </StackPanel>

                    <!-- 组4：成本与质量目标 -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="成本与质量目标" FontWeight="Bold" Margin="0,0,0,10"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="目标成本上限:" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="0" Grid.Column="1"
                                         Text="{Binding Parameters.CostTargetMax, StringFormat=F1}"
                                         Style="{StaticResource InputTextBoxStyle}"
                                         IsEnabled="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=CostOptimal}"
                                         Margin="5,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="元/吨" VerticalAlignment="Center" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="TFe最大偏差:" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="1" Grid.Column="1"
                                         Text="{Binding Parameters.TfeMaxDeviation, StringFormat=F2}"
                                         Style="{StaticResource InputTextBoxStyle}"
                                         IsEnabled="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}"
                                         Margin="5,2"/>
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Ro最大偏差:" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="2" Grid.Column="1"
                                         Text="{Binding Parameters.RoMaxDeviation, StringFormat=F3}"
                                         Style="{StaticResource InputTextBoxStyle}"
                                         IsEnabled="{Binding Parameters.OptimizationType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=QualityOptimal}"
                                         Margin="5,2"/>
                                <TextBlock Grid.Row="2" Grid.Column="2" Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </Grid>
                        </StackPanel>

                        <!-- 约束条件确认按钮 -->
                        <StackPanel Margin="0,15,0,0">
                            <Button Content="确定约束条件"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Command="{Binding ConfirmConstraintsCommand}"
                                    Width="150"
                                    Height="35"
                                    HorizontalAlignment="Center"
                                    Background="#10B981"
                                    Margin="0,0,0,10"/>
                        </StackPanel>

                        <!-- 计算按钮和操作区 -->
                        <StackPanel Margin="0,20,0,0">
                            <Button Content="开始计算"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Command="{Binding StartCalculationCommand}"
                                    IsEnabled="{Binding IsCalculating, Converter={StaticResource InverseBooleanConverter}}"
                                    Width="150"
                                    Height="40"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,10"/>

                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="验证约束"
                                        Style="{StaticResource PrimaryButtonStyle}"
                                        Command="{Binding ValidateConstraintsCommand}"
                                        Margin="0,0,10,0"/>
                                <Button Content="重置参数"
                                        Style="{StaticResource PrimaryButtonStyle}"
                                        Command="{Binding ResetParametersCommand}"/>
                            </StackPanel>

                            <!-- 计算进度 -->
                            <StackPanel Visibility="{Binding IsCalculating, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        Margin="0,10,0,0">
                                <ProgressBar Value="{Binding CalculationProgress}"
                                             Height="20"
                                             Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding StatusMessage}"
                                           HorizontalAlignment="Center"
                                           FontSize="12"/>
                            </StackPanel>
                        </StackPanel>

                </StackPanel>
            </Border>

        </Grid>
    </Grid>
</UserControl>
