<Window x:Class="SinterOptimizationClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:SinterOptimizationClient.Views"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="烧结智能配料计算系统" 
        Height="900" Width="1600"
        MinHeight="800" MinWidth="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">

    <Grid>
        <Grid.RowDefinitions>
            <!-- 导航栏 -->
            <RowDefinition Height="60"/>
            <!-- 主内容区 -->
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 导航栏 -->
        <Border Grid.Row="0" Style="{StaticResource NavigationBarStyle}">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：Logo + 系统名称 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- Logo占位 -->
                    <Border Width="45" Height="45" Background="White" CornerRadius="8" Margin="0,0,12,0">
                        <TextBlock Text="🔥" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="烧结智能配料计算系统"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="White"/>
                        <TextBlock Text="Sinter Ore Composition &amp; Index Calculation System"
                                   FontSize="10"
                                   Foreground="#E5E7EB"
                                   Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- 中间：状态指示器 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                    <Border Background="#10B981" CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Fill="White" Width="6" Height="6" Margin="0,0,4,0"/>
                            <TextBlock Text="系统运行中" FontSize="10" Foreground="White"/>
                        </StackPanel>
                    </Border>
                    <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}"
                               FontSize="11"
                               Foreground="#E5E7EB"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <!-- 右侧：页面切换按钮 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="📊 优化计算"
                            Width="110" Height="36"
                            FontSize="12" FontWeight="SemiBold"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,8,0"
                            Command="{Binding SwitchToOptimizationCommand}">
                        <Button.Background>
                            <SolidColorBrush Color="{Binding IsOptimizationSelected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter=#F97316|#1E40AF}"/>
                        </Button.Background>
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    BorderThickness="0">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Opacity" Value="0.9"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="📈 优化结果"
                            Width="110" Height="36"
                            FontSize="12" FontWeight="SemiBold"
                            Foreground="White"
                            BorderThickness="0"
                            Command="{Binding SwitchToResultCommand}">
                        <Button.Background>
                            <SolidColorBrush Color="{Binding IsResultSelected, Converter={StaticResource BooleanToColorConverter}, ConverterParameter=#F97316|#1E40AF}"/>
                        </Button.Background>
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    BorderThickness="0">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Opacity" Value="0.9"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区 -->
        <ContentControl Grid.Row="1" Content="{Binding CurrentView}"/>

    </Grid>
</Window>
